/**
 * Test file to verify that main account automatically gets all permissions
 * without needing to click on the account in the dropdown
 */

import { 
  setMainAccountPermissions, 
  getAllPermissions, 
  getAllPermissionsFormatted 
} from '../helpers/utils/permissionUtils';

// Mock localStorage for testing
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock console.log to avoid noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  error: jest.fn(),
};

describe('Main Account Auto Permissions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('main account gets all permissions automatically during initialization', () => {
    // Simulate the main account initialization process
    const result = setMainAccountPermissions();

    // Verify that all permissions (1-7) are set
    expect(result.allPermissions).toEqual([1, 2, 3, 4, 5, 6, 7]);
    
    // Verify that formatted permissions include all permission types
    expect(result.allPermissionsFormatted).toEqual({
      Post: true,
      Message: true,
      Analytics: true,
      User_Management: true,
      Brand_Management: true,
      Block_UnBlock: true,
      FeedBack: true,
    });

    // Verify localStorage was called to store permissions
    expect(localStorage.setItem).toHaveBeenCalledWith(
      'userPermissions',
      JSON.stringify([1, 2, 3, 4, 5, 6, 7])
    );

    expect(localStorage.setItem).toHaveBeenCalledWith(
      'userPermissionsFormatted',
      JSON.stringify({
        Post: true,
        Message: true,
        Analytics: true,
        User_Management: true,
        Brand_Management: true,
        Block_UnBlock: true,
        FeedBack: true,
      })
    );
  });

  test('getAllPermissions returns all permission numbers', () => {
    const permissions = getAllPermissions();
    expect(permissions).toEqual([1, 2, 3, 4, 5, 6, 7]);
  });

  test('getAllPermissionsFormatted returns all permission objects', () => {
    const permissions = getAllPermissionsFormatted();
    expect(permissions).toEqual({
      Post: true,
      Message: true,
      Analytics: true,
      User_Management: true,
      Brand_Management: true,
      Block_UnBlock: true,
      FeedBack: true,
    });
  });
});

/**
 * Integration test scenario:
 * 1. User logs in for the first time
 * 2. MultiAccountContext initializes with main account
 * 3. Main account should automatically have all permissions
 * 4. User should not need to click on their account in dropdown
 */
describe('Main Account Integration Scenario', () => {
  test('main account initialization flow sets permissions automatically', () => {
    // This test simulates the flow in MultiAccountContext.jsx
    
    // Step 1: User data exists (simulating successful login)
    const mockUserData = {
      user_id: 123,
      name: 'Test User',
      username: 'testuser',
      profile_image: 'test.jpg',
      token: 'test-token',
      email: '<EMAIL>',
      brand_id: 1
    };

    // Step 2: Main account is created during initialization
    const mainAccount = {
      userId: mockUserData.user_id,
      name: mockUserData.name,
      username: mockUserData.username,
      profileImage: mockUserData.profile_image,
      token: mockUserData.token,
      email: mockUserData.email,
      brandId: mockUserData.brand_id,
      isMainAccount: true
    };

    // Step 3: Permissions should be set automatically for main account
    const permissionsResult = setMainAccountPermissions();

    // Verify the main account gets all permissions without user interaction
    expect(permissionsResult.allPermissions).toEqual([1, 2, 3, 4, 5, 6, 7]);
    expect(mainAccount.isMainAccount).toBe(true);
    
    // Verify localStorage calls for permissions
    expect(localStorage.setItem).toHaveBeenCalledWith(
      'userPermissions',
      JSON.stringify([1, 2, 3, 4, 5, 6, 7])
    );
  });
});
