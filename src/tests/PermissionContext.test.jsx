/**
 * Test file for PermissionContext to verify account switch functionality
 * This test demonstrates that the PermissionContext properly updates when accounts are switched
 */

import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { PermissionProvider, usePermission } from '../helpers/context/PermissionContext';
import { accountSwitchEmitter } from '../helpers/context/MultiAccountContext';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Test component that uses the permission context
const TestComponent = () => {
  const { permissions, hasPermission } = usePermission();
  
  return (
    <div>
      <div data-testid="permissions">{JSON.stringify(permissions)}</div>
      <div data-testid="has-post-permission">{hasPermission(1) ? 'true' : 'false'}</div>
      <div data-testid="has-message-permission">{hasPermission(2) ? 'true' : 'false'}</div>
    </div>
  );
};

describe('PermissionContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console.log to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('loads initial permissions from localStorage', () => {
    // Mock localStorage to return initial permissions
    localStorageMock.getItem.mockReturnValue(JSON.stringify([1, 2, 3]));

    render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    expect(screen.getByTestId('permissions')).toHaveTextContent('[1,2,3]');
    expect(screen.getByTestId('has-post-permission')).toHaveTextContent('true');
    expect(screen.getByTestId('has-message-permission')).toHaveTextContent('true');
  });

  test('handles empty localStorage gracefully', () => {
    localStorageMock.getItem.mockReturnValue(null);

    render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    expect(screen.getByTestId('permissions')).toHaveTextContent('[]');
    expect(screen.getByTestId('has-post-permission')).toHaveTextContent('false');
    expect(screen.getByTestId('has-message-permission')).toHaveTextContent('false');
  });

  test('handles invalid JSON in localStorage', () => {
    localStorageMock.getItem.mockReturnValue('invalid-json');

    render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    expect(screen.getByTestId('permissions')).toHaveTextContent('[]');
    expect(console.error).toHaveBeenCalledWith('Invalid permissions in localStorage', expect.any(Error));
  });

  test('updates permissions when account is switched', async () => {
    // Initial permissions
    localStorageMock.getItem.mockReturnValue(JSON.stringify([1, 2]));

    render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    // Verify initial state
    expect(screen.getByTestId('permissions')).toHaveTextContent('[1,2]');
    expect(screen.getByTestId('has-post-permission')).toHaveTextContent('true');

    // Simulate account switch with new permissions
    localStorageMock.getItem.mockReturnValue(JSON.stringify([3, 4, 5]));

    act(() => {
      accountSwitchEmitter.emit({
        action: 'ACCOUNT_SWITCHED',
        account: { userId: 123, brandId: 456 },
        timestamp: Date.now()
      });
    });

    // Wait for the timeout in the context (100ms)
    await waitFor(() => {
      expect(screen.getByTestId('permissions')).toHaveTextContent('[3,4,5]');
    }, { timeout: 200 });

    expect(screen.getByTestId('has-post-permission')).toHaveTextContent('false');
    expect(screen.getByTestId('has-message-permission')).toHaveTextContent('false');
  });

  test('updates permissions on localStorage storage event', async () => {
    // Initial permissions
    localStorageMock.getItem.mockReturnValue(JSON.stringify([1]));

    render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    // Verify initial state
    expect(screen.getByTestId('permissions')).toHaveTextContent('[1]');

    // Simulate localStorage change
    localStorageMock.getItem.mockReturnValue(JSON.stringify([1, 2, 3, 4, 5, 6, 7]));

    act(() => {
      // Simulate storage event
      const storageEvent = new StorageEvent('storage', {
        key: 'userPermissions',
        newValue: JSON.stringify([1, 2, 3, 4, 5, 6, 7]),
        oldValue: JSON.stringify([1])
      });
      window.dispatchEvent(storageEvent);
    });

    await waitFor(() => {
      expect(screen.getByTestId('permissions')).toHaveTextContent('[1,2,3,4,5,6,7]');
    });

    expect(screen.getByTestId('has-post-permission')).toHaveTextContent('true');
    expect(screen.getByTestId('has-message-permission')).toHaveTextContent('true');
  });

  test('ignores non-ACCOUNT_SWITCHED events', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify([1, 2]));

    render(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>
    );

    // Verify initial state
    expect(screen.getByTestId('permissions')).toHaveTextContent('[1,2]');

    // Simulate different event type
    act(() => {
      accountSwitchEmitter.emit({
        action: 'SOME_OTHER_ACTION',
        timestamp: Date.now()
      });
    });

    // Wait a bit to ensure no update happens
    await new Promise(resolve => setTimeout(resolve, 150));

    // Should still have original permissions
    expect(screen.getByTestId('permissions')).toHaveTextContent('[1,2]');
  });
});
