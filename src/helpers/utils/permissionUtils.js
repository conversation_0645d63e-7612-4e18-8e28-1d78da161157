/**
 * Utility functions for handling user permissions
 *
 * Permission mapping:
 * 1 = Post
 * 2 = Message
 * 3 = Analytics
 * 4 = User_Management
 * 5 = Brand_Management
 * 6 = Block_UnBlock
 * 7 = FeedBack
 */

/**
 * Get raw permissions array from localStorage
 * @returns {Array} Array of permission numbers
 */
export const getRawPermissions = () => {
  try {
    const permissions = localStorage.getItem("userPermissions");
    return permissions ? JSON.parse(permissions) : [];
  } catch (error) {
    console.error("Error parsing raw permissions:", error);
    return [];
  }
};

/**
 * Get formatted permissions object from localStorage
 * @returns {Object} Object with permission names as keys and boolean values
 */
export const getFormattedPermissions = () => {
  try {
    const permissions = localStorage.getItem("userPermissionsFormatted");
    return permissions
      ? JSON.parse(permissions)
      : {
          Post: false,
          Message: false,
          Analytics: false,
          User_Management: false,
          Brand_Management: false,
          Block_UnBlock: false,
          FeedBack: false,
        };
  } catch (error) {
    console.error("Error parsing formatted permissions:", error);
    return {
      Post: false,
      Message: false,
      Analytics: false,
      User_Management: false,
      Brand_Management: false,
      Block_UnBlock: false,
      FeedBack: false,
    };
  }
};

/**
 * Check if user has a specific permission
 * @param {string} permissionName - Name of the permission (Post, Message, Analytics, etc.)
 * @returns {boolean} True if user has the permission, false otherwise
 */
export const hasPermission = (permissionName) => {
  const permissions = getFormattedPermissions();
  return permissions[permissionName] || false;
};

/**
 * Check if user has a specific permission by number
 * @param {number} permissionNumber - Permission number (1-7)
 * @returns {boolean} True if user has the permission, false otherwise
 */
export const hasPermissionByNumber = (permissionNumber) => {
  const permissions = getRawPermissions();
  return permissions.includes(permissionNumber);
};

/**
 * Check if user has multiple permissions
 * @param {Array} permissionNames - Array of permission names
 * @returns {boolean} True if user has ALL the permissions, false otherwise
 */
export const hasAllPermissions = (permissionNames) => {
  const permissions = getFormattedPermissions();
  return permissionNames.every(
    (permission) => permissions[permission] || false
  );
};

/**
 * Check if user has any of the specified permissions
 * @param {Array} permissionNames - Array of permission names
 * @returns {boolean} True if user has ANY of the permissions, false otherwise
 */
export const hasAnyPermission = (permissionNames) => {
  const permissions = getFormattedPermissions();
  return permissionNames.some((permission) => permissions[permission] || false);
};

/**
 * Get all permissions that the user has
 * @returns {Array} Array of permission names that the user has
 */
export const getUserPermissions = () => {
  const permissions = getFormattedPermissions();
  return Object.keys(permissions).filter(
    (permission) => permissions[permission]
  );
};

/**
 * Check if user has no permissions at all
 * @returns {boolean} True if user has no permissions, false otherwise
 */
export const hasNoPermissions = () => {
  const permissions = getRawPermissions();
  return permissions.length === 0;
};

/**
 * Permission constants for easy reference
 */
export const PERMISSIONS = {
  POST: "Post",
  MESSAGE: "Message",
  ANALYTICS: "Analytics",
  USER_MANAGEMENT: "User_Management",
  BRAND_MANAGEMENT: "Brand_Management",
  BLOCK_UNBLOCK: "Block_UnBlock",
  FEEDBACK: "FeedBack",
};

/**
 * Permission numbers for easy reference
 */
export const PERMISSION_NUMBERS = {
  POST: 1,
  MESSAGE: 2,
  ANALYTICS: 3,
  USER_MANAGEMENT: 4,
  BRAND_MANAGEMENT: 5,
  BLOCK_UNBLOCK: 6,
  FEEDBACK: 7,
};

/**
 * Get all permissions array (1-7) for main account
 * @returns {Array} Array containing all permission numbers [1, 2, 3, 4, 5, 6, 7]
 */
export const getAllPermissions = () => {
  return [1, 2, 3, 4, 5, 6, 7];
};

/**
 * Get all permissions formatted object for main account
 * @returns {Object} Object with all permissions set to true
 */
export const getAllPermissionsFormatted = () => {
  return {
    Post: true,
    Message: true,
    Analytics: true,
    User_Management: true,
    Brand_Management: true,
    Block_UnBlock: true,
    FeedBack: true,
  };
};

/**
 * Set all permissions for main account in localStorage
 * This function is used when switching to the main account
 */
export const setMainAccountPermissions = () => {
  const allPermissions = getAllPermissions();
  const allPermissionsFormatted = getAllPermissionsFormatted();

  localStorage.setItem("userPermissions", JSON.stringify(allPermissions));
  localStorage.setItem(
    "userPermissionsFormatted",
    JSON.stringify(allPermissionsFormatted)
  );

  console.log("Main account permissions set to all:", {
    raw: allPermissions,
    formatted: allPermissionsFormatted,
  });

  return { allPermissions, allPermissionsFormatted };
};
