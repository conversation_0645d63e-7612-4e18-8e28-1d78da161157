import { createContext, useContext, useEffect, useState } from "react";
import { accountSwitchEmitter } from "./MultiAccountContext";

// Create the context
const PermissionContext = createContext();

// Provider component
export const PermissionProvider = ({ children }) => {
  const [permissions, setPermissions] = useState([]);

  // Function to update permissions from localStorage
  const updatePermissions = () => {
    const stored = localStorage.getItem("userPermissions");
    try {
      const parsed = JSON.parse(stored);
      if (Array.isArray(parsed)) {
        setPermissions(parsed);
      } else {
        setPermissions([]);
      }
    } catch (error) {
      console.error("Invalid permissions in localStorage", error);
      setPermissions([]);
    }
  };

  useEffect(() => {
    // Load permissions initially
    updatePermissions();

    // Listen for account switch events to update permissions
    const unsubscribe = accountSwitchEmitter.subscribe((data) => {
      if (data.action === "ACCOUNT_SWITCHED") {
        console.log(
          "PermissionContext: Account switched, updating permissions"
        );
        // Small delay to ensure localStorage is updated
        setTimeout(updatePermissions, 100);
      }
    });

    // Listen for localStorage changes (in case permissions are updated elsewhere)
    const handleStorageChange = (event) => {
      if (event.key === "userPermissions") {
        console.log("PermissionContext: localStorage permissions changed");
        updatePermissions();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    // Cleanup function
    return () => {
      unsubscribe();
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  // Function to check a permission
  const hasPermission = (code) => permissions.includes(code);

  return (
    <PermissionContext.Provider value={{ permissions, hasPermission }}>
      {children}
    </PermissionContext.Provider>
  );
};

// Custom hook
export const usePermission = () => useContext(PermissionContext);
